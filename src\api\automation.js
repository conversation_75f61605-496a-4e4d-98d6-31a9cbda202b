import fs from "fs";
import fse from "fs-extra";
import path from "path";
import { fileURLToPath } from 'url';
import axios from "axios";
import simpleGit from "simple-git";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration constants
const TEMPLATE_REPO = "https://github.com/saudkhanbpk/inihabesha.git"
const PROJECTS_DIR = path.join(__dirname, "projects");
const ANGULAR_PROJECT_NAME = "IniHabesha";
const GITHUB_USERNAME = "saudkhanbpk";

// --- SECURITY WARNING ---
// These should be moved to environment variables in production
const GITHUB_TOKEN = '*********************************************************************************************';
const NETLIFY_AUTH_TOKEN = '****************************************';
const NETLIFY_GITHUB_INSTALLATION_ID = 27811724;

// Create projects directory if it doesn't exist
// if (!fs.existsSync(PROJECTS_DIR)) {
//     fs.mkdirSync(PROJECTS_DIR, { recursive: true });
// }
if (!fs.existsSync(PROJECTS_DIR)) fs.mkdirSync(PROJECTS_DIR);

// Function to get GitHub repository ID
async function getGitHubRepoId(repoName) {
    try {
        const response = await axios.get(`https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}`, {
            headers: {
                'Authorization': `token ${GITHUB_TOKEN}`,
                'User-Agent': 'Store-generator'
            }
        });
        return response.data.id;
    } catch (error) {
        console.error('❌ Failed to get GitHub repo ID:', error.response?.data || error.message);
        throw error;
    }
}

// Function to get Netlify GitHub installation ID
async function getNetlifyGitHubInstallationId() {
    if (NETLIFY_GITHUB_INSTALLATION_ID) {
        return NETLIFY_GITHUB_INSTALLATION_ID;
    }
    try {
        const response = await axios.get('https://api.netlify.com/api/v1/accounts', {
            headers: {
                'Authorization': `Bearer ${NETLIFY_AUTH_TOKEN}`,
                'Content-Type': 'application/json'
            }
        });
        const account = response.data[0];
        if (account && account.github_installation_id) {
            console.log(`✅ Found GitHub installation ID: ${account.github_installation_id}`);
            return account.github_installation_id;
        }
        throw new Error('No GitHub installation ID found in account');
    } catch (error) {
        console.error('❌ Could not get GitHub installation ID automatically');
        console.error('Please follow these steps:');
        console.error('1. Go to https://github.com/settings/installations');
        console.error('2. Find "Netlify" in the list and click "Configure"');
        console.error('3. Copy the number at the end of the URL (e.g., if URL is github.com/settings/installations/12345, use 12345)');
        console.error('4. Set NETLIFY_GITHUB_INSTALLATION_ID = 12345 in your environment variables');
        throw new Error('GitHub installation ID required for GitHub integration');
    }
}

// Function to deploy to Netlify with GitHub integration
async function deployToNetlifyWithGitHub(username, repoName) {
    try {
        console.log(`🔍 Getting GitHub repository information...`);
        const repoId = await getGitHubRepoId(repoName);
        console.log(`✅ GitHub repo ID: ${repoId}`);

        const installationId = await getNetlifyGitHubInstallationId();
        console.log(`✅ Netlify installation ID: ${installationId}`);

        const repoResponse = await axios.get(`https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}`, {
            headers: {
                'Authorization': `token ${GITHUB_TOKEN}`,
                'User-Agent': 'Store-generator',
                'Accept': 'application/vnd.github.v3+json'
            }
        });
        const isPrivate = repoResponse.data.private;

        const sitePayload = {
            name: `${username}-store-${Date.now()}`,
            repo: {
                provider: 'github',
                id: repoId,
                repo: `${GITHUB_USERNAME}/${repoName}`,
                private: isPrivate,
                branch: "main",
                installation_id: installationId
            },
            build_settings: {
                cmd: "npm run build",
                dir: `dist/${ANGULAR_PROJECT_NAME}`,
                repo_type: "git"
            }
        };

        console.log(`🌐 Creating Netlify site with GitHub connection...`);
        console.log(`📋 Site payload:`, JSON.stringify(sitePayload, null, 2));

        const siteResponse = await axios.post(
            'https://api.netlify.com/api/v1/sites',
            sitePayload,
            {
                headers: {
                    Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
                    "Content-Type": "application/json"
                }
            }
        );

        const deployedUrl = siteResponse.data.url;
        console.log(`🎉 Site created and connected to GitHub: ${deployedUrl}`);
        console.log(`🔄 First deployment should start automatically...`);

        return {
            success: true,
            deployedUrl,
            siteId: siteResponse.data.id,
            netlifySubdomain: siteResponse.data.name + '.netlify.app'
        };
    } catch (error) {
        console.error("❌ Netlify GitHub deployment failed:");
        if (error.response) {
            console.error("Status:", error.response.status);
            console.error("Data:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Error:", error.message);
        }
        throw error;
    }
}

// Function to set up Git repository
async function setupGitRepository(userFolder, repoCloneUrl, username) {
    const userGit = simpleGit(userFolder);
    try {
        console.log(`🔧 Setting up Git repository...`);
        await userGit.init();
        await userGit.addConfig('user.name', 'Store Generator');
        await userGit.addConfig('user.email', '<EMAIL>');

        try {
            await userGit.removeRemote("origin");
        } catch (error) {
            if (!error.message.includes('remote origin does not exist')) {
                throw error;
            }
        }

        await userGit.addRemote("origin", repoCloneUrl);

        try {
            await userGit.checkoutLocalBranch("main");
        } catch (err) {
            if (err.message.includes('already exists')) {
                await userGit.checkout("main");
            } else {
                await userGit.checkout(['-b', 'main']);
            }
        }

        await userGit.add(".");
        const status = await userGit.status();
        console.log(`📋 Git status:`, status.files.length, 'files to commit');

        if (status.files.length > 0) {
            await userGit.commit(`Initial commit for ${username} store`);
            console.log(`✅ Changes committed successfully`);
        }

        console.log(`⬆️ Pushing to GitHub...`);
        await userGit.push("origin", "main", ["--force", "--set-upstream"]);
        console.log(`✅ Successfully pushed to GitHub repository`);

        try {
            const remoteResponse = await axios.get(`https://api.github.com/repos/${GITHUB_USERNAME}/${username}-store/branches/main`, {
                headers: {
                    'Authorization': `token ${GITHUB_TOKEN}`,
                    'User-Agent': 'store-generator',
                    'Accept': 'application/vnd.github.v3+json'
                }
            });
            console.log(`✅ Main branch verified on GitHub:`, remoteResponse.data.name);
        } catch (verifyError) {
            console.warn(`⚠️ Could not verify main branch:`, verifyError.response?.status || verifyError.message);
        }

        return true;
    } catch (error) {
        console.error("❌ Git setup failed:", error);
        throw error;
    }
}

// Function to fetch file content from GitHub
async function fetchFileContentFromGitHub(repoName, filePath) {
    try {
        const response = await axios.get(
            `https://api.github.com/repos/${GITHUB_USERNAME}/${repoName}/contents/${filePath}`,
            {
                headers: {
                    'Authorization': `token ${GITHUB_TOKEN}`,
                    'User-Agent': 'store-generator',
                    'Accept': 'application/vnd.github.v3.raw'
                }
            }
        );
        return response.data;
    } catch (error) {
        console.error(`❌ Failed to fetch file content from ${filePath}:`, error.response?.data || error.message);
        throw error;
    }
}

// Normalize string: lowercase + remove all spaces
function normalize(str) {
    return str.toLowerCase().replace(/\s+/g, '');
}

// Helper: Recursively replace value in nested object
function deepReplaceValue(obj, searchStr, replaceStr) {
    const normalizedSearch = normalize(searchStr);
    if (typeof obj === 'string') {
        const normalizedObj = normalize(obj);
        if (normalizedObj === normalizedSearch) {
            console.log(`Replacing: "${obj}" => "${replaceStr}"`);
            return replaceStr;
        }
        return obj;
    } else if (Array.isArray(obj)) {
        return obj.map(item => deepReplaceValue(item, searchStr, replaceStr));
    } else if (typeof obj === 'object' && obj !== null) {
        const newObj = {};
        for (const key in obj) {
            newObj[key] = deepReplaceValue(obj[key], searchStr, replaceStr);
        }
        return newObj;
    }
    
    return obj;
}

async function updateRepositoryFiles(userFolder, files, repoName) {
    try {
        console.log(`📝 Updating files in repository...`);
        for (const file of files) {
            const { filePath, content, replace } = file;
            const fullPath = path.join(userFolder, filePath);

            if (!filePath || typeof filePath !== 'string') {
                throw new Error(`Invalid or missing filePath in file: ${JSON.stringify(file)}`);
            }

            // Ensure directory exists
            const dir = path.dirname(fullPath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            if (replace) {
                if (!replace.search || !replace.replace) {
                    throw new Error(`Invalid replace object in file: ${JSON.stringify(file)}`);
                }

                const fileRaw = fs.readFileSync(fullPath, 'utf-8');
                const json = JSON.parse(fileRaw);

                const updatedJson = deepReplaceValue(json, replace.search.trim(), replace.replace.trim());

                fs.writeFileSync(fullPath, JSON.stringify(updatedJson, null, 2), 'utf-8');
                console.log(`✅ Replaced "${replace.search}" with "${replace.replace}" in ${filePath}`);
            } else if (content) {
                fs.writeFileSync(fullPath, content, 'utf-8');
                console.log(`✅ Overwritten ${filePath}`);
            } else {
                throw new Error(`No content or replace object provided for file: ${filePath}`);
            }
        }
        return true;
    } catch (error) {
        console.error(`❌ Failed to update files:`, error.message);
        throw error;
    }
}

// Function to commit and push changes
async function commitAndPushChanges(userFolder, username, commitMessage) {
    const userGit = simpleGit(userFolder);
    try {
        console.log(`🔧 Committing and pushing changes...`);
        await userGit.add(".");
        const status = await userGit.status();
        console.log(`📋 Git status:`, status.files.length, 'files to commit');

        if (status.files.length > 0) {
            await userGit.commit(commitMessage || `Update Store for ${username}`);
            console.log(`✅ Changes committed successfully`);
        }

        console.log(`⬆️ Pushing to GitHub...`);
        await userGit.push("origin", "main");
        console.log(`✅ Successfully pushed changes to GitHub repository`);
        return true;
    } catch (error) {
        console.error(`❌ Failed to commit and push changes:`, error.message);
        throw error;
    }
}

// Function to add a custom domain to a Netlify site via API
async function addCustomDomainToNetlifySite(siteId, domainName) {
    try {
        console.log(`🔗 Adding custom domain '${domainName}' to Netlify site '${siteId}'...`);
        const response = await axios.patch(
            `https://api.netlify.com/api/v1/sites/${siteId}`,
            {
                custom_domain: domainName
            },
            {
                headers: {
                    Authorization: `Bearer ${NETLIFY_AUTH_TOKEN}`,
                    "Content-Type": "application/json"
                }
            }
        );
        console.log(`✅ Custom domain '${domainName}' added to Netlify site. Response:`, response.data.custom_domain);
        return true;
    } catch (error) {
        console.error(`❌ Failed to add custom domain to Netlify site:`);
        if (error.response) {
            console.error("Status:", error.response.status);
            console.error("Data:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Error:", error.message);
        }
        throw error;
    }
}

export const generateStore = async (req, res) => {
    const { name, about, imageUrl, linkedin, username, customDomain } = req.body;
    
    // Validate required fields
    if (!name || !username) {
        return res.status(400).json({ message: "Name and username are required fields" });
    }

    const userFolder = path.join(PROJECTS_DIR, username);
    const git = simpleGit();

    try {
        // Clean up existing folder
        if (fs.existsSync(userFolder)) {
            console.log(`🧹 Cleaning up existing folder: ${userFolder}`);
            fse.removeSync(userFolder);
        }

        // Clone template repository
        console.log(`📁 Cloning template...`);
        await git.clone(TEMPLATE_REPO, userFolder);

        // Update component with user data
        const componentPath = path.join(userFolder, "src/app/app.component.ts");
        if (fs.existsSync(componentPath)) {
            let content = fs.readFileSync(componentPath, "utf8");
            content = content
                .replace(/{{name}}/g, name || '')
                .replace(/{{about}}/g, about || '')
                .replace(/{{imageUrl}}/g, imageUrl || '')
                .replace(/{{linkedin}}/g, linkedin || '');
            fs.writeFileSync(componentPath, content);
            console.log(`✅ Updated app.component.ts with user data`);
        } else {
            console.warn(`⚠️ Component file not found at: ${componentPath}`);
        }

        // Create GitHub repository
        const repoName = `${username}-store`;
        let repoCloneUrl = "";

        console.log(`📂 Creating GitHub repository: ${repoName}...`);
        try {
            const createRepoRes = await axios.post(
                "https://api.github.com/user/repos",
                {
                    name: repoName,
                    private: true,
                    auto_init: false,
                    description: `Store website for ${name}`,
                    has_issues: false,
                    has_projects: false,
                    has_wiki: false
                },
                {
                    headers: {
                        Authorization: `token ${GITHUB_TOKEN}`,
                        "User-Agent": "store-generator",
                        "Accept": "application/vnd.github.v3+json"
                    }
                }
            );
            repoCloneUrl = createRepoRes.data.clone_url.replace(
                "https://",
                `https://${GITHUB_TOKEN}@`
            );
            console.log(`✅ New repository created: ${repoName}`);
        } catch (repoErr) {
            if (repoErr.response?.status === 422) {
                repoCloneUrl = `https://${GITHUB_TOKEN}@github.com/${GITHUB_USERNAME}/${repoName}.git`;
                console.warn(`⚠️ Repository already exists. Using existing one.`);
            } else {
                console.error("❌ GitHub repo creation failed:", repoErr.response?.data || repoErr.message);
                throw new Error("GitHub repository creation failed.");
            }
        }

        // Setup Git repository
        await setupGitRepository(userFolder, repoCloneUrl, username);

        // Wait for GitHub to process the push
        console.log(`⏳ Waiting for GitHub to process the repository...`);
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Deploy to Netlify with GitHub integration
        console.log(`🚀 Deploying to Netlify with GitHub integration...`);
        const deployResult = await deployToNetlifyWithGitHub(username, repoName);

        let customDomainStatus = "No custom domain provided.";
        if (customDomain) {
            try {
                await addCustomDomainToNetlifySite(deployResult.siteId, customDomain);
                customDomainStatus = `Custom domain '${customDomain}' added to Netlify. Please update DNS records manually to point to Netlify's servers (e.g., A record to 75.2.60.5 or CNAME to ${deployResult.netlifySubdomain}).`;
            } catch (domainErr) {
                console.error("❌ Custom domain setup failed:", domainErr.message);
                customDomainStatus = `Failed to set up custom domain '${customDomain}'. Error: ${domainErr.message}`;
            }
        }

        // Clean up local folder
        console.log(`🧹 Cleaning up local folder...`);
        fse.removeSync(userFolder);

        res.status(200).json({
            message: "✅ Store deployed successfully via GitHub and Netlify!",
            deployedUrl: deployResult.deployedUrl,
            githubUrl: `https://github.com/${GITHUB_USERNAME}/${repoName}`,
            deploymentType: "github-connected",
            customDomainStatus: customDomainStatus,
            note: "Site will auto-deploy when you push changes to the main branch"
        });
    } catch (err) {
        console.error("❌ Error:", err);
        // Clean up in case of error
        if (fs.existsSync(userFolder)) {
            fse.removeSync(userFolder);
        }
        if (!res.headersSent) {
            res.status(500).json({
                message: "Something went wrong",
                error: err.message || err.toString()
            });
        }
    }
};

export const updateStore = async (req, res) => {
    console.log('Received /update request:', JSON.stringify(req.body, null, 2));
    if (!req.body) {
        return res.status(400).json({ message: "Request body is missing" });
    }
    const { username, files, commitMessage } = req.body;
    if (typeof username !== 'string' || !username.trim()) {
        return res.status(400).json({ message: "Username must be a non-empty string" });
    }
    const userFolder = path.join(PROJECTS_DIR, username);
    const git = simpleGit();
    const repoName = `${username}-store`;
    const repoCloneUrl = `https://${GITHUB_TOKEN}@github.com/${GITHUB_USERNAME}/${repoName}.git`;

    try {
        // Validate input
        if (!files || !Array.isArray(files) || files.length === 0) {
            throw new Error("Files array is required and must not be empty");
        }

        // Clean up existing folder
        if (fs.existsSync(userFolder)) {
            console.log(`🧹 Cleaning up existing folder: ${userFolder}`);
            fse.removeSync(userFolder);
        }

        // Clone the user's store repository
        console.log(`📁 Cloning store repository: ${repoName}...`);
        await git.clone(repoCloneUrl, userFolder);

        // Update specified files
        await updateRepositoryFiles(userFolder, files, repoName);

        // Commit and push changes
        await commitAndPushChanges(userFolder, username, commitMessage);

        // Wait for GitHub to process the push
        console.log(`⏳ Waiting for GitHub to process the changes...`);
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Clean up local folder
        console.log(`🧹 Cleaning up local folder...`);
        fse.removeSync(userFolder);

        res.status(200).json({
            message: "✅ Store updated and redeploy triggered!",
            githubUrl: `https://github.com/${GITHUB_USERNAME}/${repoName}`,
            note: "Netlify will automatically redeploy the site with the new changes"
        });
    } catch (err) {
        console.error("❌ Update Error:", err);
        // Clean up in case of error
        if (fs.existsSync(userFolder)) {
            fse.removeSync(userFolder);
        }
        if (!res.headersSent) {
            res.status(400).json({
                message: "Something went wrong",
                error: err.message || err.toString()
            });
        }
    }
};

export const health = async (req, res) => {
    try {
        // Basic health check with GitHub connectivity test
        const ghResponse = await axios.get('https://api.github.com', {
            headers: {
                'Authorization': `token ${GITHUB_TOKEN}`,
                'User-Agent': 'Store-generator'
            }
        });

        res.json({
            status: "OK",
            github_connectivity: ghResponse.status === 200 ? "OK" : "Unavailable",
            github_installation_configured: !!NETLIFY_GITHUB_INSTALLATION_ID,
            netlify_configured: !!NETLIFY_AUTH_TOKEN,
            storage: {
                projects_dir: PROJECTS_DIR,
                exists: fs.existsSync(PROJECTS_DIR),
                writable: (() => {
                    try {
                        fs.accessSync(PROJECTS_DIR, fs.constants.W_OK);
                        return true;
                    } catch {
                        return false;
                    }
                })()
            }
        });
    } catch (error) {
        res.status(500).json({
            status: "Degraded",
            error: error.message,
            github_installation_configured: !!NETLIFY_GITHUB_INSTALLATION_ID,
            netlify_configured: !!NETLIFY_AUTH_TOKEN
        });
    }
};