// Email template for order ready notifications with embedded review form
export const generateOrderReadyEmailTemplate = (customerName, orderType, orderNo, businessName, deviceInfo, apiBaseUrl, orderId, customerId, userId) => {
  const isDelivery = orderType === 'delivery';
  const readyMessage = isDelivery ? 'ready for delivery' : 'ready for pickup';
  const actionMessage = isDelivery ? 'Your order will be delivered soon!' : 'Please come to pick up your order.';

  return `
    <html>
      <head>
        <style>
          .logo {
            width: 30%;
          }
          a {
            color: blue;
            cursor: pointer;
            text-decoration: none;
          }
          .maindivdata {
            padding: 2rem 4rem;
            border: 1px solid lightgray;
          }
          .client {
            color: white;
            font-weight: 700;
            display: flex;
            font-size: 25px;
            width: 100%;
            justify-content: center;
            padding-top: 10rem;
            padding-left: 20px;
          }
          .power {
            font-size: 12px;
            color: gray;
          }
          p {
            font-size: 16px;
            font-family: 'Poppins', sans-serif;
          }
          .container {
            width: 50%;
            margin: auto;
            font-family: 'Poppins', sans-serif;
          }
          .infologo {
            background: transparent;
            border: none;
          }
          .shortimg {
            width: 20px;
            height: 20px;
          }
          h3 {
            font-family: 'Poppins', sans-serif;
          }
          span {
            font-family: 'Poppins', sans-serif;
          }
          h5 {
            font-family: 'Poppins', sans-serif;
          }
          .review-section {
            background-color: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
          }
          .review-form {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin: 15px 0;
          }
          .rating-container {
            margin: 15px 0;
          }
          .rating-label {
            font-weight: bold;
            margin-bottom: 8px;
            display: block;
            color: #333;
          }
          .star-rating {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
          }
          .star {
            font-size: 24px;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s;
          }
          .star:hover,
          .star.active {
            color: #ffc107;
          }
          .form-group {
            margin-bottom: 15px;
          }
          .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
          }
          .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Poppins', sans-serif;
          }
          .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Poppins', sans-serif;
            min-height: 80px;
            resize: vertical;
          }
          .submit-button {
            background-color: #068af5;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
          }
          .submit-button:hover {
            background-color: #0056b3;
          }
          .order-details {
            background-color: #e8f4fd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #068af5;
          }
          @media screen and (max-width: 900px) {
            .container {
              width: 100%;
              margin: 0px;
            }
            .client {
              color: white;
              font-weight: 700;
              display: grid;
              font-size: 25px;
              width: 100%;
              padding-top: 10rem;
              padding-left: 10px;
            }
            .maindivdata {
              padding: 2rem 10px;
            }
            .btn {
              font-size: 12px;
            }
          }
        </style>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
      </head>
      <body>
        <div class="container">
          <div style="font-family: Arial, Helvetica, sans-serif;">
            <div style="width: auto; height: 4rem; background-color: rgb(6, 138, 245);"></div>
            <div class="maindivdata">
              <div class="top" style="display: flex; justify-content: center !important; align-items: center;">
                <img class="image" style="justify-self: center; margin-left: 20%; display: flex; justify-content: center !important; align-items: center; width: 60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
              </div>
              
              <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${customerName}</strong>,</p>
              
              <div class="order-details">
                <h3 style="margin: 0 0 10px 0; color: #068af5;">🎉 Great News! Your Order is Ready!</h3>
                <p style="margin: 5px 0;"><strong>Order Number:</strong> ${orderNo}</p>
                <p style="margin: 5px 0;"><strong>Status:</strong> Your order is ${readyMessage}</p>
                <p style="margin: 5px 0;">${actionMessage}</p>
              </div>
              
              <p style="margin-bottom: 2rem;">We are excited to have served you at <strong>${businessName}</strong>.</p>
              
              ${isDelivery ? 
                '<p style="margin-bottom: 2rem;">Our delivery team will contact you shortly with the estimated delivery time.</p>' : 
                '<p style="margin-bottom: 2rem;">Please visit us at your convenience to collect your order.</p>'
              }
              
              <div class="review-section">
                <h3 style="color: #068af5; margin-top: 0;">📝 We Value Your Feedback!</h3>
                <p>How was your experience with us? Your feedback helps us improve our service and helps other customers make informed decisions.</p>

                <div class="review-form">
                  <form id="reviewForm" action="${apiBaseUrl}/device/email-review" method="POST">
                    <input type="hidden" name="deviceId" value="${deviceInfo?._id || ''}">
                    <input type="hidden" name="customerId" value="${customerId}">
                    <input type="hidden" name="orderId" value="${orderId}">

                    <div class="rating-container">
                      <label class="rating-label">Food Quality & Taste</label>
                      <div class="star-rating" data-rating="food">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                      </div>
                      <input type="hidden" name="food" id="foodRating" value="">
                    </div>

                    <div class="rating-container">
                      <label class="rating-label">Service Experience</label>
                      <div class="star-rating" data-rating="service">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                      </div>
                      <input type="hidden" name="service" id="serviceRating" value="">
                    </div>

                    <div class="rating-container">
                      <label class="rating-label">Restaurant Ambiance</label>
                      <div class="star-rating" data-rating="ambiance">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                      </div>
                      <input type="hidden" name="ambiance" id="ambianceRating" value="">
                    </div>

                    <div class="form-group">
                      <label class="form-label" for="testimonial">Tell us about your experience (optional)</label>
                      <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your thoughts about your dining experience..."></textarea>
                    </div>

                    <button type="submit" class="submit-button">Submit Review</button>
                  </form>

                  <p style="font-size: 14px; color: #666; margin-top: 15px;">
                    <em>Your review will help us serve you better and assist other customers in their dining decisions.</em>
                  </p>
                </div>
              </div>
              
              <p style="margin-bottom: 2rem;">Thank you for choosing <strong>${businessName}</strong>!</p>
              
              <p style="margin-bottom: 2rem;">If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL></strong>.</p>
              
              <hr>
              
              <div style="display: flex; justify-content: space-between; margin-top: 1rem;">
                <div>
                  <img style="width: 60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
                </div>
                <div style="display: flex; margin-left: 45%;">
                  <a style="margin-right: 10px;" href="https://www.linkedin.com/company/patronworks/">
                    <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
                  </a>
                  <a href="https://www.facebook.com/patronworks">
                    <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script>
          // Star rating functionality
          document.addEventListener('DOMContentLoaded', function() {
            const starRatings = document.querySelectorAll('.star-rating');

            starRatings.forEach(rating => {
              const stars = rating.querySelectorAll('.star');
              const ratingType = rating.getAttribute('data-rating');
              const hiddenInput = document.getElementById(ratingType + 'Rating');

              stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                  const value = parseInt(this.getAttribute('data-value'));
                  hiddenInput.value = value;

                  // Update visual state
                  stars.forEach((s, i) => {
                    if (i < value) {
                      s.classList.add('active');
                    } else {
                      s.classList.remove('active');
                    }
                  });
                });

                star.addEventListener('mouseover', function() {
                  const value = parseInt(this.getAttribute('data-value'));
                  stars.forEach((s, i) => {
                    if (i < value) {
                      s.style.color = '#ffc107';
                    } else {
                      s.style.color = '#ddd';
                    }
                  });
                });
              });

              rating.addEventListener('mouseleave', function() {
                const currentValue = parseInt(hiddenInput.value) || 0;
                stars.forEach((s, i) => {
                  if (i < currentValue) {
                    s.style.color = '#ffc107';
                  } else {
                    s.style.color = '#ddd';
                  }
                });
              });
            });

            // Form submission
            document.getElementById('reviewForm').addEventListener('submit', function(e) {
              e.preventDefault();

              const formData = new FormData(this);
              const data = Object.fromEntries(formData);

              // Validate that at least one rating is provided
              if (!data.food && !data.service && !data.ambiance) {
                alert('Please provide at least one rating before submitting.');
                return;
              }

              // Submit the form via fetch
              fetch(this.action, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
              })
              .then(response => response.json())
              .then(result => {
                if (result.message) {
                  alert('Thank you for your review! Your feedback has been submitted successfully.');
                  this.style.display = 'none';
                  const thankYou = document.createElement('div');
                  thankYou.innerHTML = '<p style="text-align: center; color: #068af5; font-weight: bold; padding: 20px;">✅ Thank you for your valuable feedback!</p>';
                  this.parentNode.appendChild(thankYou);
                } else {
                  alert('There was an error submitting your review. Please try again.');
                }
              })
              .catch(error => {
                console.error('Error:', error);
                alert('There was an error submitting your review. Please try again.');
              });
            });
          });
        </script>
      </body>
    </html>
  `;
};
