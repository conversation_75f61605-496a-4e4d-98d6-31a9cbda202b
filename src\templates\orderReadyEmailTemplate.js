// Email template for order ready notifications
export const generateOrderReadyEmailTemplate = (customerName, orderType, orderNo, businessName, deviceInfo, reviewUrl) => {
  const isDelivery = orderType === 'delivery';
  const readyMessage = isDelivery ? 'ready for delivery' : 'ready for pickup';
  const actionMessage = isDelivery ? 'Your order will be delivered soon!' : 'Please come to pick up your order.';
  
  return `
    <html>
      <head>
        <style>
          .logo {
            width: 30%;
          }
          a {
            color: blue;
            cursor: pointer;
            text-decoration: none;
          }
          .maindivdata {
            padding: 2rem 4rem;
            border: 1px solid lightgray;
          }
          .client {
            color: white;
            font-weight: 700;
            display: flex;
            font-size: 25px;
            width: 100%;
            justify-content: center;
            padding-top: 10rem;
            padding-left: 20px;
          }
          .power {
            font-size: 12px;
            color: gray;
          }
          p {
            font-size: 16px;
            font-family: 'Poppins', sans-serif;
          }
          .container {
            width: 50%;
            margin: auto;
            font-family: 'Poppins', sans-serif;
          }
          .infologo {
            background: transparent;
            border: none;
          }
          .shortimg {
            width: 20px;
            height: 20px;
          }
          h3 {
            font-family: 'Poppins', sans-serif;
          }
          span {
            font-family: 'Poppins', sans-serif;
          }
          h5 {
            font-family: 'Poppins', sans-serif;
          }
          .review-section {
            background-color: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
          }
          .review-button {
            background-color: #068af5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            margin: 10px 0;
          }
          .review-button:hover {
            background-color: #0056b3;
            color: white;
          }
          .order-details {
            background-color: #e8f4fd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #068af5;
          }
          @media screen and (max-width: 900px) {
            .container {
              width: 100%;
              margin: 0px;
            }
            .client {
              color: white;
              font-weight: 700;
              display: grid;
              font-size: 25px;
              width: 100%;
              padding-top: 10rem;
              padding-left: 10px;
            }
            .maindivdata {
              padding: 2rem 10px;
            }
            .btn {
              font-size: 12px;
            }
          }
        </style>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
      </head>
      <body>
        <div class="container">
          <div style="font-family: Arial, Helvetica, sans-serif;">
            <div style="width: auto; height: 4rem; background-color: rgb(6, 138, 245);"></div>
            <div class="maindivdata">
              <div class="top" style="display: flex; justify-content: center !important; align-items: center;">
                <img class="image" style="justify-self: center; margin-left: 20%; display: flex; justify-content: center !important; align-items: center; width: 60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
              </div>
              
              <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${customerName}</strong>,</p>
              
              <div class="order-details">
                <h3 style="margin: 0 0 10px 0; color: #068af5;">🎉 Great News! Your Order is Ready!</h3>
                <p style="margin: 5px 0;"><strong>Order Number:</strong> ${orderNo}</p>
                <p style="margin: 5px 0;"><strong>Status:</strong> Your order is ${readyMessage}</p>
                <p style="margin: 5px 0;">${actionMessage}</p>
              </div>
              
              <p style="margin-bottom: 2rem;">We are excited to have served you at <strong>${businessName}</strong>.</p>
              
              ${isDelivery ? 
                '<p style="margin-bottom: 2rem;">Our delivery team will contact you shortly with the estimated delivery time.</p>' : 
                '<p style="margin-bottom: 2rem;">Please visit us at your convenience to collect your order.</p>'
              }
              
              <div class="review-section">
                <h3 style="color: #068af5; margin-top: 0;">📝 We Value Your Feedback!</h3>
                <p>How was your experience with us? Your feedback helps us improve our service and helps other customers make informed decisions.</p>
                <p>Please take a moment to share your thoughts about:</p>
                <ul>
                  <li>Food Quality & Taste</li>
                  <li>Service Experience</li>
                  <li>Restaurant Ambiance</li>
                </ul>
                <a href="${reviewUrl}" class="review-button">Leave a Review</a>
                <p style="font-size: 14px; color: #666; margin-top: 10px;">
                  <em>Your review will help us serve you better and assist other customers in their dining decisions.</em>
                </p>
              </div>
              
              <p style="margin-bottom: 2rem;">Thank you for choosing <strong>${businessName}</strong>!</p>
              
              <p style="margin-bottom: 2rem;">If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL></strong>.</p>
              
              <hr>
              
              <div style="display: flex; justify-content: space-between; margin-top: 1rem;">
                <div>
                  <img style="width: 60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
                </div>
                <div style="display: flex; margin-left: 45%;">
                  <a style="margin-right: 10px;" href="https://www.linkedin.com/company/patronworks/">
                    <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
                  </a>
                  <a href="https://www.facebook.com/patronworks">
                    <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
};
