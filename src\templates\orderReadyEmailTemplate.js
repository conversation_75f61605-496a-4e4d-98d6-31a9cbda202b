// Email template for order ready notifications with embedded review form
export const generateOrderReadyEmailTemplate = (customerName, orderType, orderNo, businessName, deviceInfo, apiBaseUrl, orderId, customerId, userId) => {
  const isDelivery = orderType === 'delivery';
  const readyMessage = isDelivery ? 'ready for delivery' : 'ready for pickup';
  const actionMessage = isDelivery ? 'Your order will be delivered soon!' : 'Please come to pick up your order.';

  return `
    <html>
      <head>
        <style>
          .logo {
            width: 30%;
          }
          a {
            color: blue;
            cursor: pointer;
            text-decoration: none;
          }
          .maindivdata {
            padding: 2rem 4rem;
            border: 1px solid lightgray;
          }
          .client {
            color: white;
            font-weight: 700;
            display: flex;
            font-size: 25px;
            width: 100%;
            justify-content: center;
            padding-top: 10rem;
            padding-left: 20px;
          }
          .power {
            font-size: 12px;
            color: gray;
          }
          p {
            font-size: 16px;
            font-family: 'Poppins', sans-serif;
          }
          .container {
            width: 50%;
            margin: auto;
            font-family: 'Poppins', sans-serif;
          }
          .infologo {
            background: transparent;
            border: none;
          }
          .shortimg {
            width: 20px;
            height: 20px;
          }
          h3 {
            font-family: 'Poppins', sans-serif;
          }
          span {
            font-family: 'Poppins', sans-serif;
          }
          h5 {
            font-family: 'Poppins', sans-serif;
          }
          .review-section {
            background-color: #f0f8ff;
            padding: 25px;
            border-radius: 8px;
            margin: 25px 0;
            border: 2px solid #068af5;
          }
          .review-section h3 {
            color: #068af5;
            margin: 0 0 20px 0;
            font-size: 20px;
            text-align: center;
          }
          .review-form {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin: 15px 0;
          }
          .rating-container {
            margin: 15px 0;
          }
          .rating-label {
            font-weight: bold;
            margin-bottom: 8px;
            display: block;
            color: #333;
          }
          .radio-rating {
            margin-bottom: 15px;
          }
          .radio-label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.2s;
            font-size: 14px;
          }
          .radio-label:hover {
            background-color: #f0f8ff;
          }
          .radio-label input[type="radio"] {
            margin-right: 8px;
            transform: scale(1.2);
          }
          .form-group {
            margin-bottom: 15px;
          }
          .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
          }
          .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Poppins', sans-serif;
          }
          .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Poppins', sans-serif;
            min-height: 80px;
            resize: vertical;
          }
          .submit-button {
            background-color: #068af5;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
          }
          .submit-button:hover {
            background-color: #0056b3;
          }
          .order-details {
            background-color: #e8f4fd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border-left: 4px solid #068af5;
          }
          @media screen and (max-width: 900px) {
            .container {
              width: 100%;
              margin: 0px;
            }
            .client {
              color: white;
              font-weight: 700;
              display: grid;
              font-size: 25px;
              width: 100%;
              padding-top: 10rem;
              padding-left: 10px;
            }
            .maindivdata {
              padding: 2rem 10px;
            }
            .btn {
              font-size: 12px;
            }
          }
        </style>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
      </head>
      <body>
        <div class="container">
          <div style="font-family: Arial, Helvetica, sans-serif;">
            <div style="width: auto; height: 4rem; background-color: rgb(6, 138, 245);"></div>
            <div class="maindivdata">
              <div class="top" style="display: flex; justify-content: center !important; align-items: center;">
                <img class="image" style="justify-self: center; margin-left: 20%; display: flex; justify-content: center !important; align-items: center; width: 60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
              </div>
              
              <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${customerName}</strong>,</p>
              
              <div class="order-details">
                <h3 style="margin: 0 0 10px 0; color: #068af5;">🎉 Great News! Your Order is Ready!</h3>
                <p style="margin: 5px 0;"><strong>Order Number:</strong> ${orderNo}</p>
                <p style="margin: 5px 0;"><strong>Status:</strong> Your order is ${readyMessage}</p>
                <p style="margin: 5px 0;">${actionMessage}</p>
              </div>
              
              <p style="margin-bottom: 2rem;">We are excited to have served you at <strong>${businessName}</strong>.</p>
              
              ${isDelivery ? 
                '<p style="margin-bottom: 2rem;">Our delivery team will contact you shortly with the estimated delivery time.</p>' : 
                '<p style="margin-bottom: 2rem;">Please visit us at your convenience to collect your order.</p>'
              }
              
              <div class="review-section">
                <h3 style="color: #068af5; margin-top: 0;">📝 We Value Your Feedback!</h3>
                <p>How was your experience with us? Your feedback helps us improve our service and helps other customers make informed decisions.</p>

                <div class="review-form">
                  <form id="reviewForm" action="${apiBaseUrl}/api/v1/device/email-review" method="POST">
                    <input type="hidden" name="deviceId" value="${deviceInfo?._id || ''}">
                    <input type="hidden" name="customerId" value="${customerId}">
                    <input type="hidden" name="orderId" value="${orderId}">

                    <div class="rating-container">
                      <label class="rating-label">🍽️ Food Quality & Taste</label>
                      <div class="radio-rating">
                        <label class="radio-label"><input type="radio" name="food" value="1"> ⭐ Poor</label>
                        <label class="radio-label"><input type="radio" name="food" value="2"> ⭐⭐ Fair</label>
                        <label class="radio-label"><input type="radio" name="food" value="3"> ⭐⭐⭐ Good</label>
                        <label class="radio-label"><input type="radio" name="food" value="4"> ⭐⭐⭐⭐ Very Good</label>
                        <label class="radio-label"><input type="radio" name="food" value="5"> ⭐⭐⭐⭐⭐ Excellent</label>
                      </div>
                    </div>

                    <div class="rating-container">
                      <label class="rating-label">👥 Service Experience</label>
                      <div class="radio-rating">
                        <label class="radio-label"><input type="radio" name="service" value="1"> ⭐ Poor</label>
                        <label class="radio-label"><input type="radio" name="service" value="2"> ⭐⭐ Fair</label>
                        <label class="radio-label"><input type="radio" name="service" value="3"> ⭐⭐⭐ Good</label>
                        <label class="radio-label"><input type="radio" name="service" value="4"> ⭐⭐⭐⭐ Very Good</label>
                        <label class="radio-label"><input type="radio" name="service" value="5"> ⭐⭐⭐⭐⭐ Excellent</label>
                      </div>
                    </div>

                    <div class="rating-container">
                      <label class="rating-label">🏪 Restaurant Ambiance</label>
                      <div class="radio-rating">
                        <label class="radio-label"><input type="radio" name="ambiance" value="1"> ⭐ Poor</label>
                        <label class="radio-label"><input type="radio" name="ambiance" value="2"> ⭐⭐ Fair</label>
                        <label class="radio-label"><input type="radio" name="ambiance" value="3"> ⭐⭐⭐ Good</label>
                        <label class="radio-label"><input type="radio" name="ambiance" value="4"> ⭐⭐⭐⭐ Very Good</label>
                        <label class="radio-label"><input type="radio" name="ambiance" value="5"> ⭐⭐⭐⭐⭐ Excellent</label>
                      </div>
                    </div>

                    <div class="form-group">
                      <label class="form-label" for="testimonial">💬 Tell us about your experience (optional)</label>
                      <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Tell us more about your experience at ${businessName}. What did you love most? Any suggestions for improvement?"></textarea>
                    </div>

                    <input type="submit" class="submit-button" value="Submit Your Review">
                  </form>

                  <p style="font-size: 14px; color: #666; margin-top: 15px;">
                    <em>Your review will help us serve you better and assist other customers in their dining decisions.</em>
                  </p>
                </div>
              </div>
              
              <p style="margin-bottom: 2rem;">Thank you for choosing <strong>${businessName}</strong>!</p>
              
              <p style="margin-bottom: 2rem;">If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL></strong>.</p>
              
              <hr>
              
              <div style="display: flex; justify-content: space-between; margin-top: 1rem;">
                <div>
                  <img style="width: 60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
                </div>
                <div style="display: flex; margin-left: 45%;">
                  <a style="margin-right: 10px;" href="https://www.linkedin.com/company/patronworks/">
                    <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
                  </a>
                  <a href="https://www.facebook.com/patronworks">
                    <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
};
